# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SimplePOS e-Cashier is a modular add-on that enhances existing Point of Sale systems with AI capabilities and conversational interfaces. This is currently a **prototype/demo implementation** showcasing the core adaptive interface concept before full backend integration.

**Key Positioning**: Add-on not replacement - designed to augment existing POS systems with AI-first conversational interface that progressively adapts to user expertise levels.

## Architecture

### Current Implementation (Prototype)
- **index.html**: Main application with embedded CSS and JavaScript (2000+ lines)
- **script.js**: Separate JavaScript file with application logic 
- **styles.css**: Separate CSS file with comprehensive theme system
- **No build process**: Direct HTML/CSS/JavaScript implementation
- **CDN Dependencies**: TailwindCSS, FontAwesome, Chart.js

### Core Components
- **Hybrid Interface System**: Adapts between conversation-only, traditional buttons, and smart hybrid modes
- **Natural Language Processing**: Client-side parsing for product recognition and order processing  
- **Adaptive User Profiling**: Tracks user preferences and skill level progression ('learning' → 'expert' → 'traditional')
- **Order Management**: Full cart functionality with real-time calculations
- **Smart Insights System**: Real-time analytics, upsell opportunities, combo recommendations
- **Multi-Theme System**: Business-specific themes (café, restaurant, corporate, health) with accessibility support

### Key Technologies
- **Frontend**: Vanilla JavaScript, TailwindCSS for styling, FontAwesome for icons
- **Storage**: localStorage for user preferences persistence
- **No backend**: Fully client-side application

## Development Commands

Since this is a prototype with no build process:

### Development
```bash
# Serve the file locally (any method)
python -m http.server 8000
# OR
npx serve .
# OR open index.html directly in browser
```

### File Structure
- **index.html**: Main HTML structure and embedded components
- **script.js**: Application logic, state management, NLP processing  
- **styles.css**: CSS custom properties for theming system

### No Build Pipeline
- No package.json, linting tools, or test framework configured
- Manual testing only - no automated test suite
- Direct file editing and browser refresh workflow

## Core System Architecture

### State Management (script.js)
```javascript
// Global application state
currentOrder = []           // Current shopping cart items
userPreferences = {}        // User behavior and skill level tracking  
chatHistory = []           // Conversation log for context
insightData = {}           // Smart insights and analytics
currentTheme = 'light'     // Theme system state
isProcessingCheckout = false // Checkout flow state
```

### Adaptive Intelligence System
The system learns user behavior through:
- **Conversation vs Button Usage Tracking**: Adjusts interface prominence
- **Skill Level Detection**: 'learning' → 'intermediate' → 'expert' → 'traditional'
- **Interface Mode Switching**: Smart Hybrid → Conversation Only → Traditional Only

### Natural Language Processing
Client-side text parsing handles:
- Quantity extraction (numbers and word numbers)
- Product matching with variations
- Command recognition (checkout, discount, clear)
- Order modification (add, remove items)

### Product System
Hardcoded product catalog with:
- Product variations for NLP matching
- Fixed pricing structure
- Tax calculation (8.5%)

## Key Functions and Architecture

### Message Processing Flow
```
User Input → processNaturalLanguage() → Product Matching → Order Update → UI Refresh
```

### Interface Adaptation Logic
```
User Behavior → updateUserLevel() → Interface Adjustment → Preference Storage
```

### Order Management
```
addToOrder() → updateOrderDisplay() → Calculate Totals → Enable Checkout
```

## Important Implementation Details

### User Experience Modes
- **Smart Hybrid**: Default mode combining conversation and visual elements
- **Conversation Only**: Pure text-based interaction
- **Traditional Only**: Button-only interface

### Adaptive Features
- Product grid visibility adjusts based on user skill level
- Help text changes based on user proficiency
- Interface complexity scales with user expertise

### State Persistence
User preferences saved to localStorage including:
- Preferred interaction mode
- Usage statistics (conversation vs button usage)
- Skill level progression

## Working with This Codebase

### Making Changes
- Edit index.html directly
- All styling in `<style>` tag
- All JavaScript in `<script>` tag at bottom
- Refresh browser to see changes

### Testing
- Manual testing in browser only
- Test different user interaction patterns to verify adaptive behavior
- Clear localStorage to reset user preferences for testing

### Adding Features
- **Products**: Extend the `products` array in script.js for new items
- **NLP**: Modify `processNaturalLanguage()` for new command patterns  
- **Themes**: Add new themes in styles.css using CSS custom properties
- **Insights**: Extend `insightData` structure for new analytics
- **Adaptive Logic**: Update `updateUserLevel()` for new behavior tracking

## Important Notes for Development

### Future Architecture (docs/selected_techstack.md)
The current prototype will evolve into a full-stack system using:
- **Frontend**: Vue.js 3 + TypeScript + Tailwind CSS
- **Backend**: Node.js + Express + GraphQL + MongoDB  
- **AI/NLP**: TensorFlow.js + Hugging Face Transformers
- **Infrastructure**: Docker + Kubernetes deployment

### Business Context
- **Target**: Multi-vertical POS enhancement (café, restaurant, retail)
- **Key Metrics**: 40% faster processing, 60% reduced training time
- **Progressive Enhancement**: Non-disruptive adoption for existing teams